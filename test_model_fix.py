#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修正后的FunASR模型调用
"""

import os
import sys
import numpy as np

def test_funasr_model_call():
    """测试修正后的FunASR模型调用"""
    try:
        # 导入修正后的转录器
        sys.path.insert(0, '/data/funasr')
        from funasr_api_zm import FunASRTranscriber
        
        print("初始化FunASR转录器...")
        transcriber = FunASRTranscriber("iic/SenseVoiceSmall", "cuda:1")
        
        # 创建一个简单的测试音频文件
        print("创建测试音频文件...")
        sample_rate = 16000
        duration = 2  # 2秒
        t = np.linspace(0, duration, int(sample_rate * duration))
        # 生成440Hz的正弦波
        audio_data = 0.3 * np.sin(2 * np.pi * 440 * t)
        
        # 保存为临时文件
        import soundfile as sf
        test_audio_path = "/tmp/test_audio.wav"
        sf.write(test_audio_path, audio_data, sample_rate)
        print(f"测试音频文件已保存: {test_audio_path}")
        
        # 测试转录
        print("开始测试转录...")
        text, result = transcriber.transcribe(test_audio_path)
        
        print(f"转录成功!")
        print(f"转录文本: {text}")
        
        if result and len(result) > 0:
            result_data = result[0]
            print(f"结果键: {list(result_data.keys())}")
            
            # 检查sentence_info
            if 'sentence_info' in result_data:
                sentence_info = result_data['sentence_info']
                print(f"sentence_info 数量: {len(sentence_info)}")
                
                if len(sentence_info) > 0:
                    print(f"第一个句子: {sentence_info[0]}")
        
        # 清理临时文件
        try:
            os.remove(test_audio_path)
            print("临时文件已删除")
        except:
            pass
            
        return True
        
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_srt_generation():
    """测试SRT生成功能"""
    try:
        print("\n" + "="*50)
        print("测试SRT生成功能")
        print("="*50)
        
        # 导入相关模块
        sys.path.insert(0, '/data/funasr')
        from funasr_api_zm import FunASRTranscriber
        
        # 模拟sentence_info数据
        mock_sentence_info = [
            {
                'text': '这是第一个测试句子。',
                'timestamp': [[0, 2000]]
            },
            {
                'text': '这是第二个测试句子。',
                'timestamp': [[2000, 4500]]
            }
        ]
        
        # 创建转录器实例
        transcriber = FunASRTranscriber("iic/SenseVoiceSmall", "cuda:1")
        
        # 测试SRT生成
        srt_content = transcriber.generate_srt_from_sentences(mock_sentence_info)
        
        print("生成的SRT内容:")
        print(srt_content)
        
        # 验证格式
        lines = srt_content.strip().split('\n')
        expected_patterns = [
            '1',
            '00:00:00,000 --> 00:00:02,000',
            '这是第一个测试句子',
            '',
            '2',
            '00:00:02,000 --> 00:00:04,500',
            '这是第二个测试句子'
        ]
        
        print("\n格式验证:")
        for i, expected in enumerate(expected_patterns):
            if i < len(lines):
                actual = lines[i].strip()
                match = expected in actual or actual == expected
                print(f"  行{i+1}: {'✓' if match else '✗'} 期望包含'{expected}' 实际'{actual}'")
            else:
                print(f"  行{i+1}: ✗ 缺失")
        
        return True
        
    except Exception as e:
        print(f"SRT生成测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("测试修正后的FunASR模型调用")
    print("="*60)
    
    # 测试模型调用
    model_test_result = test_funasr_model_call()
    
    # 测试SRT生成
    srt_test_result = test_srt_generation()
    
    # 总结
    print("\n" + "="*60)
    print("测试结果总结")
    print("="*60)
    print(f"模型调用测试: {'✓ 通过' if model_test_result else '✗ 失败'}")
    print(f"SRT生成测试:  {'✓ 通过' if srt_test_result else '✗ 失败'}")
    
    overall_result = model_test_result and srt_test_result
    print(f"总体结果:     {'✓ 通过' if overall_result else '✗ 失败'}")
    
    if overall_result:
        print("\n🎉 所有测试通过！修正成功。")
    else:
        print("\n⚠️  存在问题，需要进一步调试。")

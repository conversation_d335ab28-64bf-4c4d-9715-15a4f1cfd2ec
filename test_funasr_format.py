#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试FunASR模型返回的数据格式
"""

import os
import sys
import json
import numpy as np

def test_funasr_sentence_format():
    """测试FunASR模型返回的sentence_info格式"""
    try:
        from funasr import AutoModel
        
        print("初始化FunASR模型...")
        model = AutoModel(
            model="iic/SenseVoiceSmall",
            vad_model="fsmn-vad",
            vad_kwargs={"max_single_segment_time": 30000},
            device="cuda:1"
        )
        
        # 创建一个简单的测试音频（1秒的正弦波）
        sample_rate = 16000
        duration = 3  # 3秒
        t = np.linspace(0, duration, int(sample_rate * duration))
        # 生成440Hz的正弦波（A音）
        audio_data = 0.3 * np.sin(2 * np.pi * 440 * t)
        
        print("开始转录测试音频...")
        
        # 使用sentence_timestamp参数（类似example中的调用）
        result = model.generate(
            input=audio_data,
            cache={},
            language="auto",
            use_itn=True,
            batch_size_s=60,
            merge_vad=True,
            merge_length_s=15,
            sentence_timestamp=True,
            return_raw_text=True,
            is_final=True
        )
        
        print("转录结果结构:")
        if result and len(result) > 0:
            result_data = result[0]
            print(f"结果键: {list(result_data.keys())}")
            
            # 检查sentence_info
            if 'sentence_info' in result_data:
                sentence_info = result_data['sentence_info']
                print(f"\nsentence_info 数量: {len(sentence_info)}")
                
                if len(sentence_info) > 0:
                    print(f"第一个句子结构: {json.dumps(sentence_info[0], ensure_ascii=False, indent=2)}")
                    
                    # 检查时间戳格式
                    if 'timestamp' in sentence_info[0]:
                        ts = sentence_info[0]['timestamp']
                        print(f"时间戳格式: {ts}")
                        if len(ts) > 0:
                            print(f"第一个时间戳: {ts[0]} (类型: {type(ts[0][0])}, {type(ts[0][1])})")
            
            # 检查原始timestamp
            if 'timestamp' in result_data:
                raw_ts = result_data['timestamp']
                print(f"\n原始timestamp数量: {len(raw_ts)}")
                if len(raw_ts) > 0:
                    print(f"第一个原始时间戳: {raw_ts[0]} (类型: {type(raw_ts[0][0])}, {type(raw_ts[0][1])})")
                    print(f"最后一个原始时间戳: {raw_ts[-1]}")
        
        return result
        
    except Exception as e:
        print(f"测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def test_example_subtitle_utils():
    """测试example中的subtitle_utils"""
    try:
        sys.path.insert(0, '/data/funasr/example')
        from subtitle_utils import generate_srt, Text2SRT, time_convert
        
        print("\n测试example中的时间转换函数:")
        
        # 测试时间转换
        test_times = [0, 1000, 61500, 3661500]
        for ms in test_times:
            converted = time_convert(ms)
            print(f"  {ms}ms -> {converted}")
        
        # 测试Text2SRT类
        print("\n测试example中的Text2SRT类:")
        test_sentence = {
            'text': '这是一个测试句子',
            'timestamp': [[0, 2000], [2000, 4000]]  # 假设这是毫秒格式
        }
        
        t2s = Text2SRT(test_sentence['text'], test_sentence['timestamp'])
        print(f"  文本: {t2s.text()}")
        print(f"  开始时间: {t2s.start_time}")
        print(f"  结束时间: {t2s.end_time}")
        print(f"  SRT格式:\n{t2s.srt()}")
        
    except Exception as e:
        print(f"测试example时出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("=" * 60)
    print("测试FunASR数据格式和example实现")
    print("=" * 60)
    
    # 测试FunASR格式
    result = test_funasr_sentence_format()
    
    # 测试example实现
    test_example_subtitle_utils()
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)

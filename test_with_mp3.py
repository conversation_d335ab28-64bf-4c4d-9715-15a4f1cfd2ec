#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用test.mp3测试修正后的FunASR模型调用
"""

import os
import sys

def test_with_mp3_file():
    """使用test.mp3文件测试转录功能"""
    try:
        # 导入修正后的转录器
        sys.path.insert(0, '/data/funasr')
        from funasr_api_zm import FunASRTranscriber
        
        # 检查test.mp3文件是否存在
        test_file = "/data/funasr/test.mp3"
        if not os.path.exists(test_file):
            print(f"错误: 测试文件不存在: {test_file}")
            return False
        
        print(f"使用测试文件: {test_file}")
        print(f"文件大小: {os.path.getsize(test_file)} 字节")
        
        print("初始化FunASR转录器...")
        transcriber = FunASRTranscriber("iic/SenseVoiceSmall", "cuda:1")
        
        print("开始转录测试...")
        text, result = transcriber.transcribe(test_file)
        
        print("="*60)
        print("转录成功!")
        print("="*60)
        print(f"转录文本: {text}")
        print("="*60)
        
        if result and len(result) > 0:
            result_data = result[0]
            print(f"结果数据键: {list(result_data.keys())}")
            
            # 检查sentence_info
            if 'sentence_info' in result_data:
                sentence_info = result_data['sentence_info']
                print(f"sentence_info 数量: {len(sentence_info)}")
                
                if len(sentence_info) > 0:
                    print("前3个句子信息:")
                    for i, sent in enumerate(sentence_info[:3]):
                        print(f"  句子{i+1}: {sent}")
                        
                    # 测试SRT生成
                    print("\n测试SRT生成:")
                    srt_content = transcriber.generate_srt_from_sentences(sentence_info)
                    print("生成的SRT内容:")
                    print("-" * 40)
                    print(srt_content)
                    print("-" * 40)
            else:
                print("警告: 没有找到sentence_info")
                
            # 检查原始timestamp
            if 'timestamp' in result_data:
                raw_timestamps = result_data['timestamp']
                print(f"原始timestamp数量: {len(raw_timestamps)}")
                if len(raw_timestamps) > 0:
                    print(f"前3个原始时间戳: {raw_timestamps[:3]}")
        
        return True
        
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_bilingual_srt_generation():
    """测试多语言SRT生成"""
    try:
        print("\n" + "="*60)
        print("测试多语言SRT生成")
        print("="*60)
        
        # 导入相关模块
        sys.path.insert(0, '/data/funasr')
        from funasr_api_zm import FunASRTranscriber
        
        test_file = "/data/funasr/test.mp3"
        if not os.path.exists(test_file):
            print(f"错误: 测试文件不存在: {test_file}")
            return False
        
        print("初始化转录器...")
        transcriber = FunASRTranscriber("iic/SenseVoiceSmall", "cuda:1")
        
        print("开始转录...")
        text, result = transcriber.transcribe(test_file)
        
        if not result:
            print("转录失败，无法测试SRT生成")
            return False
        
        print("测试多语言SRT生成...")
        base_srt_path = "/tmp/test_srt"
        
        # 生成多语言SRT文件
        srt_paths = transcriber.generate_bilingual_srt(
            text, 
            base_srt_path, 
            transcription_result=result, 
            target_languages=['en']  # 只测试英语，避免翻译服务问题
        )
        
        print("生成的SRT文件:")
        for lang, path in srt_paths.items():
            print(f"  {lang}: {path}")
            
            if os.path.exists(path):
                print(f"    文件大小: {os.path.getsize(path)} 字节")
                
                # 显示文件内容的前几行
                with open(path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    lines = content.split('\n')
                    print(f"    前10行内容:")
                    for i, line in enumerate(lines[:10]):
                        print(f"      {i+1}: {line}")
            else:
                print(f"    警告: 文件未生成")
        
        return True
        
    except Exception as e:
        print(f"多语言SRT生成测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("使用test.mp3测试修正后的FunASR功能")
    print("="*60)
    
    # 测试基本转录功能
    transcribe_result = test_with_mp3_file()
    
    # 如果基本转录成功，测试SRT生成
    if transcribe_result:
        srt_result = test_bilingual_srt_generation()
    else:
        srt_result = False
    
    # 总结
    print("\n" + "="*60)
    print("测试结果总结")
    print("="*60)
    print(f"基本转录测试: {'✓ 通过' if transcribe_result else '✗ 失败'}")
    print(f"SRT生成测试:  {'✓ 通过' if srt_result else '✗ 失败'}")
    
    overall_result = transcribe_result and srt_result
    print(f"总体结果:     {'✓ 通过' if overall_result else '✗ 失败'}")
    
    if overall_result:
        print("\n🎉 所有测试通过！修正成功。")
    else:
        print("\n⚠️  存在问题，需要进一步调试。")

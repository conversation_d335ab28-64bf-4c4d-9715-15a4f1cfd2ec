#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证funasr_api_zm.py与example实现的一致性
"""

import sys
import os

def test_time_convert_consistency():
    """测试时间转换函数的一致性"""
    print("=" * 60)
    print("测试时间转换函数一致性")
    print("=" * 60)
    
    # 导入example中的实现
    sys.path.insert(0, '/data/funasr/example')
    from subtitle_utils import time_convert as example_time_convert
    
    # 导入funasr_api_zm中的实现
    sys.path.insert(0, '/data/funasr')
    from funasr_api_zm import time_convert as api_time_convert
    
    # 测试用例
    test_cases = [0, 1000, 61500, 3661500, 125000]
    
    print("毫秒值 -> example结果 | api结果 | 一致性")
    print("-" * 60)
    
    all_consistent = True
    for ms in test_cases:
        example_result = example_time_convert(ms)
        api_result = api_time_convert(ms)
        consistent = example_result == api_result
        status = "✓" if consistent else "✗"
        
        print(f"{ms:>8} -> {example_result} | {api_result} | {status}")
        
        if not consistent:
            all_consistent = False
    
    print("-" * 60)
    print(f"时间转换函数一致性: {'✓ 通过' if all_consistent else '✗ 失败'}")
    return all_consistent

def test_text2srt_consistency():
    """测试Text2SRT类的一致性"""
    print("\n" + "=" * 60)
    print("测试Text2SRT类一致性")
    print("=" * 60)
    
    # 导入example中的实现
    sys.path.insert(0, '/data/funasr/example')
    from subtitle_utils import Text2SRT as ExampleText2SRT
    
    # 导入funasr_api_zm中的实现
    sys.path.insert(0, '/data/funasr')
    from funasr_api_zm import Text2SRT as APIText2SRT
    
    # 测试数据
    test_text = "这是一个测试句子"
    test_timestamp = [[1000, 3000], [3000, 5000]]  # 毫秒格式
    
    # 创建实例
    example_t2s = ExampleText2SRT(test_text, test_timestamp)
    api_t2s = APIText2SRT(test_text, test_timestamp)
    
    print("属性对比:")
    print(f"文本内容:")
    print(f"  example: {example_t2s.text()}")
    print(f"  api:     {api_t2s.text()}")
    print(f"  一致性:  {'✓' if example_t2s.text() == api_t2s.text() else '✗'}")
    
    print(f"开始时间:")
    print(f"  example: {example_t2s.start_time}")
    print(f"  api:     {api_t2s.start_time}")
    print(f"  一致性:  {'✓' if example_t2s.start_time == api_t2s.start_time else '✗'}")
    
    print(f"结束时间:")
    print(f"  example: {example_t2s.end_time}")
    print(f"  api:     {api_t2s.end_time}")
    print(f"  一致性:  {'✓' if example_t2s.end_time == api_t2s.end_time else '✗'}")
    
    print(f"SRT格式:")
    example_srt = example_t2s.srt()
    api_srt = api_t2s.srt()
    print(f"  example:\n{example_srt}")
    print(f"  api:\n{api_srt}")
    print(f"  一致性:  {'✓' if example_srt == api_srt else '✗'}")
    
    # 检查所有属性是否一致
    all_consistent = (
        example_t2s.text() == api_t2s.text() and
        example_t2s.start_time == api_t2s.start_time and
        example_t2s.end_time == api_t2s.end_time and
        example_srt == api_srt
    )
    
    print("-" * 60)
    print(f"Text2SRT类一致性: {'✓ 通过' if all_consistent else '✗ 失败'}")
    return all_consistent

def test_generate_srt_consistency():
    """测试generate_srt函数的一致性"""
    print("\n" + "=" * 60)
    print("测试generate_srt函数一致性")
    print("=" * 60)
    
    # 导入example中的实现
    sys.path.insert(0, '/data/funasr/example')
    from subtitle_utils import generate_srt as example_generate_srt
    
    # 导入funasr_api_zm中的实现
    sys.path.insert(0, '/data/funasr')
    from funasr_api_zm import FunASRTranscriber
    
    # 模拟sentence_info数据
    sentence_list = [
        {
            'text': '这是第一个句子。',
            'timestamp': [[0, 2000]]
        },
        {
            'text': '这是第二个句子。',
            'timestamp': [[2000, 4500]]
        },
        {
            'text': '这是第三个句子。',
            'timestamp': [[4500, 7000]]
        }
    ]
    
    # 使用example的实现
    example_result = example_generate_srt(sentence_list)
    
    # 使用api的实现（创建一个模拟的转录器）
    class MockTranscriber:
        def generate_srt_from_sentences(self, sentence_list):
            from funasr_api_zm import Text2SRT
            srt_total = ''
            for i, sent in enumerate(sentence_list):
                t2s = Text2SRT(sent['text'], sent['timestamp'])
                if 'spk' in sent:
                    srt_total += "{}  spk{}\n{}".format(i + 1, sent['spk'], t2s.srt())
                else:
                    srt_total += "{}\n{}\n".format(i + 1, t2s.srt())
            return srt_total
    
    mock_transcriber = MockTranscriber()
    api_result = mock_transcriber.generate_srt_from_sentences(sentence_list)
    
    print("SRT生成结果对比:")
    print("Example结果:")
    print(example_result)
    print("\nAPI结果:")
    print(api_result)
    
    consistent = example_result == api_result
    print(f"一致性: {'✓ 通过' if consistent else '✗ 失败'}")
    
    if not consistent:
        print("\n差异分析:")
        example_lines = example_result.split('\n')
        api_lines = api_result.split('\n')
        max_lines = max(len(example_lines), len(api_lines))
        
        for i in range(max_lines):
            ex_line = example_lines[i] if i < len(example_lines) else ""
            api_line = api_lines[i] if i < len(api_lines) else ""
            if ex_line != api_line:
                print(f"  行{i+1}: example='{ex_line}' | api='{api_line}'")
    
    return consistent

if __name__ == "__main__":
    print("验证funasr_api_zm.py与example实现的一致性")
    
    try:
        # 运行所有测试
        test1_result = test_time_convert_consistency()
        test2_result = test_text2srt_consistency()
        test3_result = test_generate_srt_consistency()
        
        # 总结
        print("\n" + "=" * 60)
        print("一致性检查总结")
        print("=" * 60)
        print(f"时间转换函数: {'✓ 通过' if test1_result else '✗ 失败'}")
        print(f"Text2SRT类:   {'✓ 通过' if test2_result else '✗ 失败'}")
        print(f"SRT生成函数:  {'✓ 通过' if test3_result else '✗ 失败'}")
        
        overall_result = test1_result and test2_result and test3_result
        print("-" * 60)
        print(f"总体一致性:   {'✓ 通过' if overall_result else '✗ 失败'}")
        
        if overall_result:
            print("\n🎉 所有测试通过！funasr_api_zm.py与example实现保持一致。")
        else:
            print("\n⚠️  存在不一致，需要进一步修正。")
            
    except Exception as e:
        print(f"\n测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
